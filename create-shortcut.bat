@echo off
echo Desktop shortcut olusturuluyor...

REM PowerShell komutu ile shortcut olustur
powershell -Command "& {$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\Desktop\AD Web Manager.lnk'); $Shortcut.TargetPath = '%~dp0start-server.bat'; $Shortcut.WorkingDirectory = '%~dp0'; $Shortcut.IconLocation = '%SystemRoot%\System32\shell32.dll,13'; $Shortcut.Description = 'AD Web Manager Server Baslatici'; $Shortcut.Save()}"

echo.
echo Desktop shortcut basariyla olusturuldu!
echo "AD Web Manager" adinda bir shortcut masaustunde olusturuldu.
echo.
pause
