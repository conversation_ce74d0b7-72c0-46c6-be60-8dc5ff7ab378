@echo off
title AD Web Manager - <PERSON><PERSON> Ku<PERSON>lumu
color 0E

echo.
echo ========================================
echo   AD Web Manager - <PERSON><PERSON>
echo ========================================
echo.

REM Check if running as Administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo [ERROR] Bu script Administrator yet<PERSON>i ile calistirilmalidir!
    echo.
    echo Cozum:
    echo 1. Command Prompt'u "Run as Administrator" ile acin
    echo 2. Bu script'i tekrar calistirin
    echo.
    pause
    exit /b 1
)

echo [INFO] Administrator yetkisi dogrulandi ✓
echo.

REM Check Node.js installation
echo [INFO] Node.js kontrol ediliyor...
node --version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo [ERROR] Node.js bulunamadi!
    echo.
    echo Kurulum adımlari:
    echo 1. https://nodejs.org adresinden LTS versiyonu indirin
    echo 2. <PERSON><PERSON><PERSON> dosyasini calistirin
    echo 3. "Add to PATH" secenegini isaretleyin
    echo 4. Bu script'i tekrar calistirin
    echo.
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
echo [INFO] Node.js %NODE_VERSION% bulundu ✓

REM Check npm installation
npm --version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo [ERROR] npm bulunamadi!
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('npm --version') do set NPM_VERSION=%%i
echo [INFO] npm %NPM_VERSION% bulundu ✓
echo.

REM Install dependencies
echo [INFO] Bagimliliklar yukleniyor...
echo [INFO] Bu islem 2-5 dakika surebilir...
echo.
npm install
if %ERRORLEVEL% neq 0 (
    echo [ERROR] Bagimlilik yuklemesi basarisiz!
    pause
    exit /b 1
)
echo [INFO] Bagimliliklar basariyla yuklendi ✓
echo.

REM Build production
echo [INFO] Production build yapiliyor...
echo [INFO] Bu islem 1-2 dakika surebilir...
echo.
npm run build
if %ERRORLEVEL% neq 0 (
    echo [ERROR] Build basarisiz!
    pause
    exit /b 1
)
echo [INFO] Production build tamamlandi ✓
echo.

echo ========================================
echo   Kurulum Tamamlandi!
echo ========================================
echo.
echo [INFO] Sonraki adimlar:
echo.
echo 1. .env.production dosyasini duzenleyin:
echo    - NEXTAUTH_URL=http://YENİ_DOMAIN
echo    - DOMAIN_NAME=YENİ_DOMAIN
echo.
echo 2. DNS ayarlarini yapin:
echo    - A Record: YENİ_DOMAIN → SUNUCU_IP
echo.
echo 3. Firewall ayarlarini yapin:
echo    - Port 80'i acin
echo.
echo 4. Server'i baslatın:
echo    - start-domain.bat (Domain icin)
echo    - start-development.bat (Gelistirme icin)
echo.
echo ========================================
pause
