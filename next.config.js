/** @type {import('next').NextConfig} */
const nextConfig = {
  // Keep Next.js development indicator in default position (bottom-left)
  devIndicators: {
    position: 'bottom-left',
  },

  // HTTP configuration for development
  async rewrites() {
    return []
  },

  // Allow cross-origin requests from mobile devices and domains
  allowedDevOrigins: ['192.20.60.12:3000'],

  // Domain configuration for production
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin',
          },
        ],
      },
    ];
  },

  // External packages configuration
  serverExternalPackages: ['ldapjs'],

  // Suppress hydration warnings caused by browser extensions
  reactStrictMode: false,

  // Disable ESLint during build for production
  eslint: {
    ignoreDuringBuilds: true,
  },

  // Disable TypeScript checking during build for production
  typescript: {
    ignoreBuildErrors: true,
  },

  // Webpack configuration to handle client-side issues
  webpack: (config, { isServer }) => {
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
      };
    }
    return config;
  },
}

module.exports = nextConfig
