// Ana test script
const { execSync } = require('child_process');
const fs = require('fs');

console.log('🚀 AD Web Manager - Kapsamlı Test Süreci\n');
console.log('=' .repeat(50));

const runTest = (testName, testFile) => {
  console.log(`\n🧪 ${testName} Testi Çalıştırılıyor...`);
  console.log('-'.repeat(30));
  
  try {
    execSync(`node ${testFile}`, { stdio: 'inherit' });
    console.log(`✅ ${testName} testi tamamlandı`);
  } catch (error) {
    console.log(`❌ ${testName} testi başarısız`);
  }
};

// Test sırası
const tests = [
  ['Logo Kontrolü', 'test-logo.js'],
  ['LDAP Ayarları', 'test-ldap-settings.js'],
  ['Bağımlılıklar', 'test-dependencies.js'],
  ['API Endpoints', 'test-api.js'],
  ['Build Süreci', 'test-build.js']
];

// Testleri sırayla çalıştır
tests.forEach(([name, file]) => {
  runTest(name, file);
});

console.log('\n' + '='.repeat(50));
console.log('🏁 Tüm testler tamamlandı!');
console.log('\n💡 Sonraki adımlar:');
console.log('   1. Servisi başlatın: npm run dev');
console.log('   2. http://localhost:3000 adresine gidin');
console.log('   3. Login: admin / 581326Ob');
console.log('   4. Password Expiry sayfasını test edin');