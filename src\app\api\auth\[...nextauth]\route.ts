import NextAuth, { NextAuthOptions } from 'next-auth';
import Credentials<PERSON>rovider from 'next-auth/providers/credentials';
import { getUserByUsername, validatePassword, updateLastLogin, initializeUsers } from '@/lib/auth';

// Initialize users file on startup
initializeUsers();

const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        username: { label: 'Username', type: 'text' },
        password: { label: 'Password', type: 'password' }
      },
      async authorize(credentials) {
        if (!credentials?.username || !credentials?.password) {
          return null;
        }

        const user = getUserByUsername(credentials.username);

        if (!user) {
          return null;
        }

        const isValidPassword = validatePassword(credentials.password, user.password);

        if (!isValidPassword) {
          return null;
        }

        // Update last login
        updateLastLogin(user.id);

        return {
          id: user.id,
          name: user.username,
          email: user.username,
          role: user.role,
          permissions: user.permissions,
        };
      },
    })
  ],
  callbacks: {
    async jwt({ token, user, trigger, session }) {
      if (user) {
        token.role = user.role;
        token.permissions = user.permissions;
      }

      // Handle session updates
      if (trigger === 'update' && session) {
        token.role = session.role;
        token.permissions = session.permissions;
      }

      return token;
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.sub!;
        session.user.role = token.role as string;
        session.user.permissions = token.permissions as any;
      }
      return session;
    },
  },
  pages: {
    signIn: '/login',
  },
  callbacks: {
    async jwt({ token, user, trigger, session }) {
      if (user) {
        token.role = user.role;
        token.permissions = user.permissions;
      }

      // Handle session updates
      if (trigger === 'update' && session) {
        token.role = session.role;
        token.permissions = session.permissions;
      }

      return token;
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.sub!;
        session.user.role = token.role as string;
        session.user.permissions = token.permissions as any;
      }
      return session;
    },
    async redirect({ url, baseUrl }) {
      // Always redirect to the current domain's login page
      if (url.startsWith('/')) {
        return `${baseUrl}${url}`;
      }
      // If it's a full URL, check if it's the same domain
      if (url.startsWith(baseUrl)) {
        return url;
      }
      // For logout, always redirect to current domain's login
      return `${baseUrl}/login`;
    },
  },
  session: {
    strategy: 'jwt' as const,
  },
  secret: process.env.NEXTAUTH_SECRET || 'your-secret-key-here',
};

const handler = NextAuth(authOptions);

export { handler as GET, handler as POST, authOptions };
