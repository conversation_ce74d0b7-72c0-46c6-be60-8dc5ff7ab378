import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';

// Simple in-memory store for session invalidation
// In production, this should be in a database or Redis
let invalidatedSessions = new Set<string>();

export async function POST() {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if user has permission to manage users
    if (!session.user.permissions?.manageUsers) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    // Add current timestamp to force all sessions to refresh
    const invalidationTime = Date.now().toString();
    invalidatedSessions.add(invalidationTime);

    return NextResponse.json({ 
      success: true,
      invalidationTime 
    });
  } catch (error) {
    console.error('Error invalidating sessions:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET() {
  // Return the latest invalidation time
  const times = Array.from(invalidatedSessions);
  const latestTime = times.length > 0 ? Math.max(...times.map(Number)) : 0;
  
  return NextResponse.json({ 
    latestInvalidation: latestTime 
  });
}
