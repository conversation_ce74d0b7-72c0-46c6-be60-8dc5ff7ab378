import { NextResponse } from 'next/server';
import ldap from 'ldapjs';
import fs from 'fs';
import path from 'path';

const SETTINGS_FILE = path.join(process.cwd(), 'ldap-settings.json');

interface LdapSettings {
  server: string;
  port: string;
  baseDN: string;
  username: string;
  password: string;
  useSSL: boolean;
}

interface LockedUser {
  username: string;
  displayName: string;
  lockoutTime: string;
  passwordExpires?: string;
  accountExpires?: string;
  lastLogon?: string;
}

function loadSettings(): LdapSettings | null {
  try {
    if (fs.existsSync(SETTINGS_FILE)) {
      const data = fs.readFileSync(SETTINGS_FILE, 'utf8');
      return JSON.parse(data);
    }
    return null;
  } catch (error) {
    console.error('Error loading settings:', error);
    return null;
  }
}

export async function GET() {
  try {
    const settings = loadSettings();
    
    if (!settings) {
      return NextResponse.json(
        { error: 'LDAP ayarları bulunamadı. Lütfen önce ayarları yapılandırın.' },
        { status: 400 }
      );
    }

    if (!settings.server || !settings.baseDN || !settings.username || !settings.password) {
      return NextResponse.json(
        { error: 'LDAP ayarları eksik. Lütfen ayarları kontrol edin.' },
        { status: 400 }
      );
    }

    const protocol = settings.useSSL ? 'ldaps' : 'ldap';
    const url = `${protocol}://${settings.server}:${settings.port}`;
    
    const client = ldap.createClient({
      url: url,
      timeout: 30000,
      connectTimeout: 10000,
      tlsOptions: {
        rejectUnauthorized: false
      }
    });

    return new Promise((resolve) => {
      let isResolved = false;

      const resolveOnce = (response: NextResponse) => {
        if (!isResolved) {
          isResolved = true;
          client.destroy();
          resolve(response);
        }
      };

      const timeout = setTimeout(() => {
        resolveOnce(NextResponse.json(
          { error: 'LDAP bağlantısı zaman aşımına uğradı' },
          { status: 408 }
        ));
      }, 35000);

      client.on('error', (err) => {
        clearTimeout(timeout);
        console.error('LDAP connection error:', err);
        resolveOnce(NextResponse.json(
          { error: `LDAP bağlantı hatası: ${err.message}` },
          { status: 500 }
        ));
      });

      client.bind(settings.username, settings.password, (err) => {
        if (err) {
          clearTimeout(timeout);
          console.error('LDAP bind error:', err);
          resolveOnce(NextResponse.json(
            { error: `LDAP kimlik doğrulama hatası: ${err.message}` },
            { status: 401 }
          ));
          return;
        }

        // Search for locked users and users with expired passwords
        const searchFilter = '(&(objectClass=user)(|(lockoutTime>=1)(userAccountControl:1.2.840.113556.1.4.803:=8388608)))';
        const searchOptions = {
          scope: 'sub' as const,
          filter: searchFilter,
          attributes: [
            'sAMAccountName',
            'displayName',
            'lockoutTime',
            'pwdLastSet',
            'accountExpires',
            'lastLogon',
            'userAccountControl',
            'msDS-UserPasswordExpiryTimeComputed'
          ]
        };

        const lockedUsers: LockedUser[] = [];

        client.search(settings.baseDN, searchOptions, (searchErr, searchRes) => {
          if (searchErr) {
            clearTimeout(timeout);
            console.error('LDAP search error:', searchErr);
            resolveOnce(NextResponse.json(
              { error: `LDAP arama hatası: ${searchErr.message}` },
              { status: 500 }
            ));
            return;
          }

          searchRes.on('searchEntry', (entry) => {
            try {
              const attributes = entry.pojo.attributes;
              const getAttributeValue = (name: string) => {
                const attr = attributes.find((a: any) => a.type === name);
                return attr && attr.values && attr.values.length > 0 ? attr.values[0] : '';
              };

              const lockoutTime = getAttributeValue('lockoutTime');
              const pwdLastSet = getAttributeValue('pwdLastSet');
              const accountExpires = getAttributeValue('accountExpires');
              const lastLogon = getAttributeValue('lastLogon');
              const passwordExpiryComputed = getAttributeValue('msDS-UserPasswordExpiryTimeComputed');

              // Convert Windows FILETIME to JavaScript Date
              const convertFileTime = (fileTime: string) => {
                if (!fileTime || fileTime === '0' || fileTime === '9223372036854775807') return '';
                try {
                  const windowsEpoch = new Date('1601-01-01T00:00:00Z').getTime();
                  const jsDate = new Date(windowsEpoch + (parseInt(fileTime) / 10000));
                  return jsDate.toISOString();
                } catch {
                  return '';
                }
              };

              // Get password expiration date
              let passwordExpires = '';
              if (passwordExpiryComputed && passwordExpiryComputed !== '0') {
                // Use the computed password expiry time from AD
                passwordExpires = convertFileTime(passwordExpiryComputed);
              } else if (pwdLastSet && pwdLastSet !== '0') {
                // Fallback: calculate based on pwdLastSet + 90 days
                try {
                  const pwdDate = convertFileTime(pwdLastSet);
                  if (pwdDate) {
                    const expireDate = new Date(pwdDate);
                    expireDate.setDate(expireDate.getDate() + 90); // 90 days default
                    passwordExpires = expireDate.toISOString();
                  }
                } catch {
                  passwordExpires = '';
                }
              }

              const user: LockedUser = {
                username: getAttributeValue('sAMAccountName'),
                displayName: getAttributeValue('displayName'),
                lockoutTime: convertFileTime(lockoutTime),
                passwordExpires,
                accountExpires: convertFileTime(accountExpires),
                lastLogon: convertFileTime(lastLogon)
              };

              if (user.username) {
                lockedUsers.push(user);
              }
            } catch (entryErr) {
              console.error('Error processing search entry:', entryErr);
            }
          });

          searchRes.on('error', (searchResErr) => {
            clearTimeout(timeout);
            console.error('LDAP search result error:', searchResErr);
            resolveOnce(NextResponse.json(
              { error: `LDAP arama sonuç hatası: ${searchResErr.message}` },
              { status: 500 }
            ));
          });

          searchRes.on('end', () => {
            clearTimeout(timeout);

            // Calculate statistics
            const now = new Date();
            const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

            const stats = {
              totalLocked: lockedUsers.filter(u => u.lockoutTime).length,
              lockedToday: lockedUsers.filter(u => {
                if (!u.lockoutTime) return false;
                const lockDate = new Date(u.lockoutTime);
                return lockDate >= today;
              }).length,
              passwordExpired: lockedUsers.filter(u => {
                if (!u.passwordExpires) return false;
                return new Date(u.passwordExpires) <= now;
              }).length,
              lastUpdated: now.toISOString()
            };

            resolveOnce(NextResponse.json({
              success: true,
              users: lockedUsers,
              count: lockedUsers.length,
              stats
            }));
          });
        });
      });
    });

  } catch (error) {
    console.error('Get locked users error:', error);
    return NextResponse.json(
      { error: 'Kilitlenen kullanıcılar alınırken hata oluştu' },
      { status: 500 }
    );
  }
}
