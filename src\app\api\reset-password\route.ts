import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import ldap from 'ldapjs';

interface ResetPasswordRequest {
  username: string;
}

interface UserInfo {
  username: string;
  displayName: string;
  email?: string;
  department?: string;
  lastLogon?: string;
  passwordLastSet?: string;
  userAccountControl: number;
  isDisabled: boolean;
}

// LDAP bağlantı ayarları
const LDAP_URL = process.env.LDAP_URL || 'ldap://10.20.2.40:389';
const LDAP_BIND_DN = process.env.LDAP_BIND_DN || 'CN=Administrator,CN=Users,DC=efsan,DC=local';
const LDAP_BIND_PASSWORD = process.env.LDAP_BIND_PASSWORD || 'Ef123456';
const LDAP_BASE_DN = process.env.LDAP_BASE_DN || 'DC=efsan,DC=local';

function convertWindowsTimeToDate(windowsTime: string): Date | null {
  try {
    const windowsTimestamp = parseInt(windowsTime);
    if (windowsTimestamp === 0) return null;
    
    // Windows FILETIME epoch başlangıcı: 1 Ocak 1601
    // Unix epoch başlangıcı: 1 Ocak 1970
    // Aralarındaki fark: *********** saniye
    const unixTimestamp = (windowsTimestamp / ********) - ***********;
    return new Date(unixTimestamp * 1000);
  } catch (error) {
    console.error('Error converting Windows time:', error);
    return null;
  }
}

async function searchUser(username: string): Promise<UserInfo | null> {
  return new Promise((resolve, reject) => {
    const client = ldap.createClient({
      url: LDAP_URL,
      timeout: 10000,
      connectTimeout: 10000,
    });

    client.bind(LDAP_BIND_DN, LDAP_BIND_PASSWORD, (err) => {
      if (err) {
        console.error('LDAP bind error:', err);
        client.destroy();
        return reject(new Error('LDAP authentication failed'));
      }

      console.log(`Searching for user: ${username}`);

      const searchOptions = {
        filter: `(&(objectClass=user)(objectCategory=person)(sAMAccountName=${username}))`,
        scope: 'sub' as const,
        attributes: [
          'sAMAccountName',
          'displayName', 
          'mail',
          'department',
          'lastLogon',
          'pwdLastSet',
          'userAccountControl'
        ]
      };

      client.search(LDAP_BASE_DN, searchOptions, (err, res) => {
        if (err) {
          console.error('LDAP search error:', err);
          client.destroy();
          return reject(new Error('LDAP search failed'));
        }

        let userFound = false;

        res.on('searchEntry', (entry) => {
          userFound = true;
          const attributes = entry.pojo.attributes;
          
          const userAccountControl = parseInt(attributes.find((attr: any) => attr.type === 'userAccountControl')?.values[0] || '0');
          const isDisabled = (userAccountControl & 2) !== 0; // ADS_UF_ACCOUNTDISABLE = 2

          const userInfo: UserInfo = {
            username: attributes.find((attr: any) => attr.type === 'sAMAccountName')?.values[0] || username,
            displayName: attributes.find((attr: any) => attr.type === 'displayName')?.values[0] || username,
            email: attributes.find((attr: any) => attr.type === 'mail')?.values[0],
            department: attributes.find((attr: any) => attr.type === 'department')?.values[0],
            lastLogon: attributes.find((attr: any) => attr.type === 'lastLogon')?.values[0],
            passwordLastSet: attributes.find((attr: any) => attr.type === 'pwdLastSet')?.values[0],
            userAccountControl,
            isDisabled
          };

          console.log(`Found user: ${userInfo.username}, disabled: ${isDisabled}`);
          client.destroy();
          resolve(userInfo);
        });

        res.on('error', (err) => {
          console.error('LDAP search result error:', err);
          client.destroy();
          reject(new Error('LDAP search failed'));
        });

        res.on('end', () => {
          if (!userFound) {
            console.log(`User not found: ${username}`);
            client.destroy();
            resolve(null);
          }
        });
      });
    });

    client.on('error', (err) => {
      console.error('LDAP client error:', err);
      client.destroy();
      reject(new Error('LDAP connection failed'));
    });
  });
}

async function resetUserPassword(username: string): Promise<{ success: boolean; newPassword?: string; error?: string }> {
  return new Promise((resolve, reject) => {
    const client = ldap.createClient({
      url: LDAP_URL,
      timeout: 10000,
      connectTimeout: 10000,
    });

    client.bind(LDAP_BIND_DN, LDAP_BIND_PASSWORD, (err) => {
      if (err) {
        console.error('LDAP bind error:', err);
        client.destroy();
        return reject(new Error('LDAP authentication failed'));
      }

      // Yeni şifre oluştur (8 karakter: büyük harf, küçük harf, rakam)
      const generatePassword = () => {
        const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        const lowercase = 'abcdefghijklmnopqrstuvwxyz';
        const numbers = '0123456789';
        
        let password = '';
        password += uppercase[Math.floor(Math.random() * uppercase.length)];
        password += lowercase[Math.floor(Math.random() * lowercase.length)];
        password += numbers[Math.floor(Math.random() * numbers.length)];
        
        const allChars = uppercase + lowercase + numbers;
        for (let i = 3; i < 8; i++) {
          password += allChars[Math.floor(Math.random() * allChars.length)];
        }
        
        // Karıştır
        return password.split('').sort(() => Math.random() - 0.5).join('');
      };

      const newPassword = generatePassword();
      
      // Kullanıcının DN'ini bul
      const searchOptions = {
        filter: `(&(objectClass=user)(objectCategory=person)(sAMAccountName=${username}))`,
        scope: 'sub' as const,
        attributes: ['distinguishedName']
      };

      client.search(LDAP_BASE_DN, searchOptions, (err, res) => {
        if (err) {
          console.error('LDAP search error:', err);
          client.destroy();
          return reject(new Error('User search failed'));
        }

        let userDN = '';

        res.on('searchEntry', (entry) => {
          const attributes = entry.pojo.attributes;
          userDN = attributes.find((attr: any) => attr.type === 'distinguishedName')?.values[0] || '';
        });

        res.on('error', (err) => {
          console.error('LDAP search result error:', err);
          client.destroy();
          reject(new Error('User search failed'));
        });

        res.on('end', () => {
          if (!userDN) {
            client.destroy();
            return resolve({ success: false, error: 'User not found' });
          }

          // Şifreyi sıfırla
          const unicodePassword = Buffer.from(`"${newPassword}"`, 'utf16le');
          
          const change = new ldap.Change({
            operation: 'replace',
            modification: {
              type: 'unicodePwd',
              values: [unicodePassword]
            }
          });

          client.modify(userDN, change, (err) => {
            client.destroy();
            
            if (err) {
              console.error('Password reset error:', err);
              return resolve({ 
                success: false, 
                error: 'Password reset failed: ' + err.message 
              });
            }

            console.log(`Password reset successful for user: ${username}`);
            resolve({ 
              success: true, 
              newPassword 
            });
          });
        });
      });
    });

    client.on('error', (err) => {
      console.error('LDAP client error:', err);
      client.destroy();
      reject(new Error('LDAP connection failed'));
    });
  });
}

// Kullanıcı arama endpoint'i
export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.permissions?.resetPasswords) {
      return NextResponse.json(
        { error: 'Unauthorized - Reset password permission required' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const username = searchParams.get('username');

    if (!username) {
      return NextResponse.json(
        { error: 'Username parameter is required' },
        { status: 400 }
      );
    }

    const userInfo = await searchUser(username);
    
    if (!userInfo) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Hassas bilgileri temizle
    const safeUserInfo = {
      username: userInfo.username,
      displayName: userInfo.displayName,
      email: userInfo.email,
      department: userInfo.department,
      lastLogon: userInfo.lastLogon ? convertWindowsTimeToDate(userInfo.lastLogon)?.toISOString() : null,
      passwordLastSet: userInfo.passwordLastSet ? convertWindowsTimeToDate(userInfo.passwordLastSet)?.toISOString() : null,
      isDisabled: userInfo.isDisabled
    };

    return NextResponse.json({ user: safeUserInfo });

  } catch (error) {
    console.error('Search user error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Şifre sıfırlama endpoint'i
export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.permissions?.resetPasswords) {
      return NextResponse.json(
        { error: 'Unauthorized - Reset password permission required' },
        { status: 403 }
      );
    }

    const body: ResetPasswordRequest = await request.json();
    const { username } = body;

    if (!username) {
      return NextResponse.json(
        { error: 'Username is required' },
        { status: 400 }
      );
    }

    // Önce kullanıcının var olduğunu kontrol et
    const userInfo = await searchUser(username);
    if (!userInfo) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    if (userInfo.isDisabled) {
      return NextResponse.json(
        { error: 'Cannot reset password for disabled user' },
        { status: 400 }
      );
    }

    // Şifreyi sıfırla
    const result = await resetUserPassword(username);

    if (!result.success) {
      return NextResponse.json(
        { error: result.error || 'Password reset failed' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Password reset successfully',
      newPassword: result.newPassword,
      resetBy: session.user.username,
      resetAt: new Date().toISOString()
    });

  } catch (error) {
    console.error('Reset password error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
