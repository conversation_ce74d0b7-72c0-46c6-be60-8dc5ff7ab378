import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

const SETTINGS_FILE = path.join(process.cwd(), 'ldap-settings.json');

interface LdapSettings {
  server: string;
  port: string;
  baseDN: string;
  username: string;
  password: string;
  useSSL: boolean;
}

const defaultSettings: LdapSettings = {
  server: '',
  port: '389',
  baseDN: '',
  username: '',
  password: '',
  useSSL: false
};

// GET - Load settings
export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const includePassword = url.searchParams.get('includePassword') === 'true';

    if (fs.existsSync(SETTINGS_FILE)) {
      const data = fs.readFileSync(SETTINGS_FILE, 'utf8');
      const settings = JSON.parse(data);

      // Return password only if specifically requested (for internal use)
      return NextResponse.json({
        ...settings,
        password: includePassword ? settings.password : (settings.password ? '••••••••' : '')
      });
    } else {
      return NextResponse.json(defaultSettings);
    }
  } catch (error) {
    console.error('Error loading settings:', error);
    return NextResponse.json(
      { error: 'Ayarlar yüklenirken hata oluştu' },
      { status: 500 }
    );
  }
}

// POST - Save settings
export async function POST(request: NextRequest) {
  try {
    const settings: LdapSettings = await request.json();
    
    // Validate required fields
    if (!settings.server || !settings.baseDN || !settings.username) {
      return NextResponse.json(
        { error: 'Server, Base DN ve Username alanları zorunludur' },
        { status: 400 }
      );
    }

    // If password is masked, load the existing password
    if (settings.password === '••••••••') {
      if (fs.existsSync(SETTINGS_FILE)) {
        const existingData = fs.readFileSync(SETTINGS_FILE, 'utf8');
        const existingSettings = JSON.parse(existingData);
        settings.password = existingSettings.password;
      }
    }

    // Save settings to file
    fs.writeFileSync(SETTINGS_FILE, JSON.stringify(settings, null, 2));
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error saving settings:', error);
    return NextResponse.json(
      { error: 'Ayarlar kaydedilirken hata oluştu' },
      { status: 500 }
    );
  }
}
