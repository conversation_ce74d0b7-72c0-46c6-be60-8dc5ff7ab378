import { NextRequest, NextResponse } from 'next/server';
import ldap from 'ldapjs';

interface LdapSettings {
  server: string;
  port: string;
  baseDN: string;
  username: string;
  password: string;
  useSSL: boolean;
}

export async function POST(request: NextRequest) {
  try {
    const settings: LdapSettings = await request.json();
    const startTime = Date.now();

    // Validate required fields
    if (!settings.server || !settings.baseDN || !settings.username || !settings.password) {
      return NextResponse.json(
        { error: 'Tüm alanlar doldurulmalıdır' },
        { status: 400 }
      );
    }

    const protocol = settings.useSSL ? 'ldaps' : 'ldap';
    const url = `${protocol}://${settings.server}:${settings.port}`;

    console.log('LDAP Test Connection:', {
      url,
      username: settings.username,
      baseDN: settings.baseDN,
      useSSL: settings.useSSL
    });

    const client = ldap.createClient({
      url: url,
      timeout: 10000,
      connectTimeout: 10000,
      tlsOptions: {
        rejectUnauthorized: false // For self-signed certificates
      }
    });

    return new Promise((resolve) => {
      let isResolved = false;

      const resolveOnce = (response: NextResponse) => {
        if (!isResolved) {
          isResolved = true;
          client.destroy();
          resolve(response);
        }
      };

      // Set timeout
      const timeout = setTimeout(() => {
        resolveOnce(NextResponse.json(
          { error: 'Bağlantı zaman aşımına uğradı' },
          { status: 408 }
        ));
      }, 15000);

      client.on('error', (err) => {
        clearTimeout(timeout);
        console.error('LDAP connection error:', err);
        resolveOnce(NextResponse.json(
          { error: `Bağlantı hatası: ${err.message}` },
          { status: 500 }
        ));
      });

      client.bind(settings.username, settings.password, (err) => {
        clearTimeout(timeout);
        
        if (err) {
          console.error('LDAP bind error:', err);
          resolveOnce(NextResponse.json(
            { error: `Kimlik doğrulama hatası: ${err.message}` },
            { status: 401 }
          ));
        } else {
          // Test a simple search to verify the connection works
          const searchOptions = {
            scope: 'base' as const,
            filter: '(objectClass=*)',
            attributes: ['dn']
          };

          client.search(settings.baseDN, searchOptions, (searchErr, searchRes) => {
            if (searchErr) {
              console.error('LDAP search error:', searchErr);
              resolveOnce(NextResponse.json(
                { error: `Base DN hatası: ${searchErr.message}` },
                { status: 400 }
              ));
            } else {
              let hasResults = false;
              
              searchRes.on('searchEntry', () => {
                hasResults = true;
              });

              searchRes.on('error', (searchResErr) => {
                console.error('LDAP search result error:', searchResErr);
                resolveOnce(NextResponse.json(
                  { error: `Arama hatası: ${searchResErr.message}` },
                  { status: 500 }
                ));
              });

              searchRes.on('end', () => {
                const endTime = Date.now();
                const latency = endTime - startTime;

                resolveOnce(NextResponse.json({
                  success: true,
                  message: 'LDAP bağlantısı başarılı!',
                  latency: latency,
                  serverInfo: `${settings.server}:${settings.port}`
                }));
              });
            }
          });
        }
      });
    });

  } catch (error) {
    console.error('Test connection error:', error);
    return NextResponse.json(
      { error: 'Bağlantı testi sırasında hata oluştu' },
      { status: 500 }
    );
  }
}
