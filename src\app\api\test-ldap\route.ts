import { NextRequest, NextResponse } from 'next/server';
import ldap from 'ldapjs';
import fs from 'fs';
import path from 'path';

const SETTINGS_FILE = path.join(process.cwd(), 'ldap-settings.json');

interface LdapSettings {
  server: string;
  port: string;
  baseDN: string;
  username: string;
  password: string;
  useSSL: boolean;
}

function loadSettings(): LdapSettings | null {
  try {
    if (fs.existsSync(SETTINGS_FILE)) {
      const data = fs.readFileSync(SETTINGS_FILE, 'utf8');
      return JSON.parse(data);
    }
    return null;
  } catch (error) {
    console.error('Error loading settings:', error);
    return null;
  }
}

export async function GET() {
  try {
    const settings = loadSettings();
    
    if (!settings) {
      return NextResponse.json(
        { error: 'LDAP ayarları bulunamadı' },
        { status: 400 }
      );
    }

    const protocol = settings.useSSL ? 'ldaps' : 'ldap';
    const url = `${protocol}://${settings.server}:${settings.port}`;
    
    console.log(`Testing LDAP connection to: ${url}`);
    
    const client = ldap.createClient({
      url: url,
      timeout: 10000,
      connectTimeout: 5000,
      tlsOptions: {
        rejectUnauthorized: false
      }
    });

    return new Promise((resolve) => {
      let isResolved = false;

      const resolveOnce = (response: NextResponse) => {
        if (!isResolved) {
          isResolved = true;
          client.destroy();
          resolve(response);
        }
      };

      const timeout = setTimeout(() => {
        console.error('LDAP test timeout');
        resolveOnce(NextResponse.json(
          { error: 'LDAP bağlantı testi zaman aşımına uğradı' },
          { status: 408 }
        ));
      }, 15000);

      client.on('error', (err) => {
        clearTimeout(timeout);
        console.error('LDAP test connection error:', err);
        resolveOnce(NextResponse.json(
          { error: `LDAP bağlantı hatası: ${err.message}` },
          { status: 500 }
        ));
      });

      client.bind(settings.username, settings.password, (err) => {
        clearTimeout(timeout);
        
        if (err) {
          console.error('LDAP test bind error:', err);
          resolveOnce(NextResponse.json(
            { error: `LDAP kimlik doğrulama hatası: ${err.message}` },
            { status: 401 }
          ));
          return;
        }

        console.log('LDAP test bind successful');
        resolveOnce(NextResponse.json({
          success: true,
          message: 'LDAP bağlantısı başarılı',
          server: url,
          baseDN: settings.baseDN
        }));
      });
    });

  } catch (error: any) {
    console.error('LDAP test error:', error);
    return NextResponse.json(
      { error: `Test hatası: ${error.message}` },
      { status: 500 }
    );
  }
}
