// Dashboard stats interface'ine ekle:
interface DashboardStats {
  totalUsers: number;
  lockedUsers: number;
  expiredPasswords: number;
  expiringSoonPasswords: number; // <PERSON><PERSON> alan
}

// Dashboard component'inde:
const [stats, setStats] = useState<DashboardStats>({
  totalUsers: 0,
  lockedUsers: 0,
  expiredPasswords: 0,
  expiringSoonPasswords: 0 // Yeni alan
});

// loadDashboardStats fonksiyonunda:
const loadDashboardStats = async () => {
  try {
    const [usersResponse, passwordResponse] = await Promise.all([
      fetch('/api/users'),
      fetch('/api/password-expiry')
    ]);

    const usersData = await usersResponse.json();
    const passwordData = await passwordResponse.json();

    if (usersResponse.ok && passwordResponse.ok) {
      setStats({
        totalUsers: usersData.users?.length || 0,
        lockedUsers: usersData.users?.filter((u: any) => u.isLocked).length || 0,
        expiredPasswords: passwordData.stats?.totalExpiredUsers || 0,
        expiringSoonPasswords: passwordData.stats?.expiringSoon || 0 // Yeni veri
      });
    }
  } catch (error) {
    console.error('Dashboard stats error:', error);
  }
};

// Dashboard kartlarına ekle:
<Card className="border-purple-200">
  <CardHeader className="pb-3">
    <div className="flex items-center justify-center space-x-3">
      <AlertTriangle className="h-6 w-6 text-purple-600" />
      <CardTitle className="text-base font-semibold text-muted-foreground">Yakında Dolacak Şifreler</CardTitle>
    </div>
  </CardHeader>
  <CardContent className="text-center">
    <div className="text-4xl font-bold text-purple-600">{stats.expiringSoonPasswords}</div>
    <p className="text-xs text-muted-foreground mt-2">5 gün içinde</p>
  </CardContent>
</Card>