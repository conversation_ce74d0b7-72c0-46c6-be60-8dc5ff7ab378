'use client';

import { useSession } from 'next-auth/react';

export default function Debug() {
  const { data: session } = useSession();

  if (!session) {
    return <div>No session</div>;
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-4">Debug - User Permissions</h1>
      
      <div className="bg-gray-100 p-4 rounded-lg">
        <h2 className="text-lg font-semibold mb-2">User Info:</h2>
        <pre className="text-sm">
          {JSON.stringify(session.user, null, 2)}
        </pre>
      </div>
      
      <div className="mt-4 bg-blue-100 p-4 rounded-lg">
        <h2 className="text-lg font-semibold mb-2">Permissions Check:</h2>
        <ul className="space-y-1">
          <li>viewUsers: {session.user.permissions.viewUsers ? '✅' : '❌'}</li>
          <li>unlockUsers: {session.user.permissions.unlockUsers ? '✅' : '❌'}</li>
          <li>manageSettings: {session.user.permissions.manageSettings ? '✅' : '❌'}</li>
          <li>manageUsers: {session.user.permissions.manageUsers ? '✅' : '❌'}</li>
          <li>resetPasswords: {session.user.permissions.resetPasswords ? '✅' : '❌'}</li>
        </ul>
      </div>
    </div>
  );
}
