'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useSession } from 'next-auth/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useDeviceDetection } from '@/hooks/useDeviceDetection';
import { MobileNav } from '@/components/ui/mobile-nav';
import { DesktopNav } from '@/components/ui/desktop-nav';
import { Loader2, UserPlus, Edit, Trash2, CheckCircle, XCircle } from 'lucide-react';

interface User {
  id: string;
  username: string;
  role: 'admin' | 'user';
  permissions: {
    viewUsers: boolean;
    unlockUsers: boolean;
    manageSettings: boolean;
    manageUsers: boolean;
    resetPasswords: boolean;
  };
  createdAt: string;
  lastLogin?: string;
}

interface NewUser {
  username: string;
  password: string;
  role: 'admin' | 'user';
  permissions: {
    viewUsers: boolean;
    unlockUsers: boolean;
    manageSettings: boolean;
    manageUsers: boolean;
    resetPasswords: boolean;
  };
}

export default function ManageUsers() {
  const { data: session, update: updateSession } = useSession();
  const device = useDeviceDetection();
  const [users, setUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [messageType, setMessageType] = useState<'success' | 'error' | ''>('');
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  
  const [newUser, setNewUser] = useState<NewUser>({
    username: '',
    password: '',
    role: 'user',
    permissions: {
      viewUsers: true,
      unlockUsers: false,
      manageSettings: false,
      manageUsers: false,
      resetPasswords: false,
    }
  });

  useEffect(() => {
    if (session?.user.permissions.manageUsers) {
      loadUsers();
    }
  }, [session]);

  if (!session) {
    return null;
  }

  if (!session.user.permissions.manageUsers) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <Card>
          <CardHeader>
            <CardTitle>Erişim Reddedildi</CardTitle>
            <CardDescription>Bu sayfaya erişim yetkiniz bulunmamaktadır.</CardDescription>
          </CardHeader>
          <CardContent>
            <Button asChild>
              <Link href="/">Ana Sayfaya Dön</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  const refreshCurrentUserSession = async () => {
    try {
      console.log('🔄 Refreshing session for current user...');
      const response = await fetch('/api/auth/refresh-session', {
        method: 'POST',
      });

      if (response.ok) {
        const data = await response.json();
        console.log('✅ Session refresh data:', data);
        // Update the session with new user data
        await updateSession(data.user);
        console.log('✅ Session updated successfully');
      } else {
        console.error('❌ Session refresh failed:', response.status);
      }
    } catch (error) {
      console.error('❌ Error refreshing session:', error);
    }
  };

  const loadUsers = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/users');
      if (response.ok) {
        const data = await response.json();
        setUsers(data.users);
      } else {
        setMessage('Kullanıcılar yüklenirken hata oluştu!');
        setMessageType('error');
      }
    } catch (error) {
      setMessage('Bağlantı hatası!');
      setMessageType('error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateUser = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setMessage('');

    try {
      const response = await fetch('/api/users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newUser),
      });

      const result = await response.json();

      if (response.ok) {
        setMessage('Kullanıcı başarıyla oluşturuldu!');
        setMessageType('success');
        setIsDialogOpen(false);
        setNewUser({
          username: '',
          password: '',
          role: 'user',
          permissions: {
            viewUsers: true,
            unlockUsers: false,
            manageSettings: false,
            manageUsers: false,
            resetPasswords: false,
          }
        });
        loadUsers();
      } else {
        setMessage(result.error || 'Kullanıcı oluşturulurken hata oluştu!');
        setMessageType('error');
      }
    } catch (error) {
      setMessage('Bağlantı hatası!');
      setMessageType('error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteUser = async (userId: string) => {
    if (!confirm('Bu kullanıcıyı silmek istediğinizden emin misiniz?')) {
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch(`/api/users/${userId}`, {
        method: 'DELETE',
      });

      const result = await response.json();

      if (response.ok) {
        setMessage('Kullanıcı başarıyla silindi!');
        setMessageType('success');
        loadUsers();
      } else {
        setMessage(result.error || 'Kullanıcı silinirken hata oluştu!');
        setMessageType('error');
      }
    } catch (error) {
      setMessage('Bağlantı hatası!');
      setMessageType('error');
    } finally {
      setIsLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleString('tr-TR');
    } catch {
      return dateString;
    }
  };

  const handleEditUser = (user: User) => {
    setEditingUser(user);
    setNewUser({
      username: user.username,
      password: '', // Password will be empty for editing
      role: user.role,
      permissions: { ...user.permissions }
    });
    setIsDialogOpen(true);
  };

  const handleUpdateUser = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!editingUser) return;

    setIsLoading(true);
    setMessage('');

    try {
      const response = await fetch(`/api/users/${editingUser.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          role: newUser.role,
          permissions: newUser.permissions,
          ...(newUser.password && { password: newUser.password }) // Only include password if provided
        }),
      });

      const result = await response.json();

      if (response.ok) {
        setMessage('Kullanıcı başarıyla güncellendi!');
        setMessageType('success');
        setIsDialogOpen(false);
        setEditingUser(null);
        setNewUser({
          username: '',
          password: '',
          role: 'user',
          permissions: {
            viewUsers: false,
            unlockUsers: false,
            manageSettings: false,
            manageUsers: false,
            resetPasswords: false,
          }
        });
        loadUsers();

        // Check if the updated user is the current user (by username - more reliable)
        const currentUsername = session?.user?.username || session?.user?.name;
        const isCurrentUser = editingUser.username === currentUsername;

        console.log('Debug - User update check:', {
          editingUsername: editingUser.username,
          currentUsername: currentUsername,
          sessionUser: session?.user,
          isCurrentUser: isCurrentUser
        });

        if (isCurrentUser) {
          console.log('Current user permissions updated, forcing logout...');
          setMessage('Yetkiniz güncellendi. Yeniden giriş yapmanız gerekiyor...');
          setMessageType('success');

          setTimeout(() => {
            console.log('Signing out...');
            signOut({ callbackUrl: '/login' });
          }, 2000);
        } else {
          console.log('Different user updated, no logout needed');
        }

        // Alternative: Force logout for ANY permission change (more aggressive but safer)
        // Uncomment the lines below if the above doesn't work
        /*
        setMessage('Kullanıcı yetkileri güncellendi. Güvenlik için yeniden giriş yapmanız gerekiyor...');
        setMessageType('success');

        setTimeout(() => {
          signOut({ callbackUrl: '/login' });
        }, 2000);
        */
      } else {
        setMessage(result.error || 'Kullanıcı güncellenirken hata oluştu!');
        setMessageType('error');
      }
    } catch (error) {
      setMessage('Bağlantı hatası!');
      setMessageType('error');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Adaptive Navigation */}
      {device.isMobile ? (
        <MobileNav currentPath="/manage-users" />
      ) : (
        <DesktopNav currentPath="/manage-users" />
      )}

      {/* Main Content */}
      <main className={`
        ${device.isMobile ? 'pb-safe px-4 py-4' : 'container mx-auto px-4 py-6'}
      `}>
        <div className={`${device.isMobile ? 'space-y-4' : 'space-y-6'}`}>
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold text-foreground">Kullanıcı Yönetimi</h1>
              <p className="text-muted-foreground">Sistem kullanıcılarını yönetin</p>
            </div>

            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
              <DialogTrigger asChild>
                <Button onClick={() => {
                  setEditingUser(null);
                  setNewUser({
                    username: '',
                    password: '',
                    role: 'user',
                    permissions: {
                      viewUsers: true,
                      unlockUsers: false,
                      manageSettings: false,
                      manageUsers: false,
                      resetPasswords: false,
                    }
                  });
                }}>
                  <UserPlus className="mr-2 h-4 w-4" />
                  Yeni Kullanıcı
                </Button>
              </DialogTrigger>
                <DialogContent className="max-w-md">
                  <DialogHeader>
                    <DialogTitle>
                      {editingUser ? 'Kullanıcı Düzenle' : 'Yeni Kullanıcı Oluştur'}
                    </DialogTitle>
                    <DialogDescription>
                      {editingUser
                        ? 'Kullanıcı bilgilerini ve yetkilerini güncelleyin.'
                        : 'Yeni bir kullanıcı hesabı oluşturun ve yetkilerini belirleyin.'
                      }
                    </DialogDescription>
                  </DialogHeader>
                  <form onSubmit={editingUser ? handleUpdateUser : handleCreateUser} className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="username">Kullanıcı Adı</Label>
                      <Input
                        id="username"
                        value={newUser.username}
                        onChange={(e) => setNewUser({...newUser, username: e.target.value})}
                        placeholder="Kullanıcı adı"
                        disabled={!!editingUser}
                        required={!editingUser}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="password">
                        {editingUser ? 'Yeni Şifre (Opsiyonel)' : 'Şifre'}
                      </Label>
                      <Input
                        id="password"
                        type="password"
                        value={newUser.password}
                        onChange={(e) => setNewUser({...newUser, password: e.target.value})}
                        placeholder={editingUser ? 'Boş bırakılırsa değişmez' : 'Şifre'}
                        required={!editingUser}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="role">Rol</Label>
                      <Select value={newUser.role} onValueChange={(value: 'admin' | 'user') => setNewUser({...newUser, role: value})}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="user">User</SelectItem>
                          <SelectItem value="admin">Admin</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-3">
                      <Label>Yetkiler</Label>
                      <div className="space-y-2">
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="viewUsers"
                            checked={newUser.permissions.viewUsers}
                            onCheckedChange={(checked) => 
                              setNewUser({
                                ...newUser, 
                                permissions: {...newUser.permissions, viewUsers: checked as boolean}
                              })
                            }
                          />
                          <Label htmlFor="viewUsers">Kullanıcıları Görüntüle</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="unlockUsers"
                            checked={newUser.permissions.unlockUsers}
                            onCheckedChange={(checked) => 
                              setNewUser({
                                ...newUser, 
                                permissions: {...newUser.permissions, unlockUsers: checked as boolean}
                              })
                            }
                          />
                          <Label htmlFor="unlockUsers">Kullanıcı Unlock</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="manageSettings"
                            checked={newUser.permissions.manageSettings}
                            onCheckedChange={(checked) => 
                              setNewUser({
                                ...newUser, 
                                permissions: {...newUser.permissions, manageSettings: checked as boolean}
                              })
                            }
                          />
                          <Label htmlFor="manageSettings">Ayarları Yönet</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="manageUsers"
                            checked={newUser.permissions.manageUsers}
                            onCheckedChange={(checked) =>
                              setNewUser({
                                ...newUser,
                                permissions: {...newUser.permissions, manageUsers: checked as boolean}
                              })
                            }
                          />
                          <Label htmlFor="manageUsers">Kullanıcıları Yönet</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="resetPasswords"
                            checked={newUser.permissions.resetPasswords}
                            onCheckedChange={(checked) =>
                              setNewUser({
                                ...newUser,
                                permissions: {...newUser.permissions, resetPasswords: checked as boolean}
                              })
                            }
                          />
                          <Label htmlFor="resetPasswords">Şifre Sıfırla</Label>
                        </div>
                      </div>
                    </div>
                    <Button type="submit" className="w-full" disabled={isLoading}>
                      {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                      {isLoading
                        ? (editingUser ? 'Güncelleniyor...' : 'Oluşturuluyor...')
                        : (editingUser ? 'Kullanıcı Güncelle' : 'Kullanıcı Oluştur')
                      }
                    </Button>
                  </form>
                </DialogContent>
            </Dialog>
          </div>

          {message && (
            <Alert className={messageType === 'error' ? 'border-destructive' : 'border-green-500'}>
              <AlertDescription className={messageType === 'error' ? 'text-destructive' : 'text-green-700'}>
                {message}
              </AlertDescription>
            </Alert>
          )}

          <Card>
            <CardHeader>
              <CardTitle>Kullanıcılar</CardTitle>
              <CardDescription>
                Sistem kullanıcılarının listesi ve yönetimi
              </CardDescription>
            </CardHeader>
            <CardContent>

            {isLoading ? (
              <div className="flex justify-center items-center py-8">
                <Loader2 className="h-8 w-8 animate-spin" />
                <span className="ml-2">Yükleniyor...</span>
              </div>
            ) : (
              device.isMobile ? (
                // Mobile Card Layout
                <div className="space-y-3">
                  {users.map((user) => (
                    <Card key={user.id} className="p-4">
                      <div className="flex justify-between items-start mb-3">
                        <div className="flex-1">
                          <h3 className="font-semibold text-lg text-foreground">{user.username}</h3>
                          <div className="flex items-center gap-2 mt-1">
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                              user.role === 'admin' ? 'bg-red-100 text-red-800' : 'bg-blue-100 text-blue-800'
                            }`}>
                              {user.role === 'admin' ? 'Admin' : 'Kullanıcı'}
                            </span>
                          </div>
                        </div>
                      </div>

                      <div className="space-y-2 mb-4">
                        <div>
                          <span className="text-sm text-muted-foreground">Yetkiler:</span>
                          <div className="flex flex-wrap gap-1 mt-1">
                            {user.permissions.viewUsers && (
                              <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs">👁️ Görüntüle</span>
                            )}
                            {user.permissions.unlockUsers && (
                              <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">🔓 Unlock</span>
                            )}
                            {user.permissions.manageSettings && (
                              <span className="bg-purple-100 text-purple-800 px-2 py-1 rounded text-xs">⚙️ Ayarlar</span>
                            )}
                            {user.permissions.manageUsers && (
                              <span className="bg-orange-100 text-orange-800 px-2 py-1 rounded text-xs">👥 Kullanıcılar</span>
                            )}
                            {user.permissions.resetPasswords && (
                              <span className="bg-red-100 text-red-800 px-2 py-1 rounded text-xs">🔐 Şifre Sıfırla</span>
                            )}
                          </div>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-muted-foreground">Oluşturulma:</span>
                          <span className="font-medium">{formatDate(user.createdAt)}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-muted-foreground">Son Giriş:</span>
                          <span className="font-medium">{user.lastLogin ? formatDate(user.lastLogin) : 'Hiç giriş yapmamış'}</span>
                        </div>
                      </div>

                      <div className="flex gap-2">
                        <Button
                          className="flex-1"
                          variant="outline"
                          onClick={() => handleEditUser(user)}
                        >
                          <Edit className="mr-2 h-4 w-4" />
                          Düzenle
                        </Button>
                        {user.username !== 'admin' && (
                          <Button
                            variant="destructive"
                            onClick={() => handleDeleteUser(user.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </Card>
                  ))}
                </div>
              ) : (
                // Desktop Table Layout
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Kullanıcı Adı</TableHead>
                        <TableHead>Rol</TableHead>
                        <TableHead>Yetkiler</TableHead>
                        <TableHead>Oluşturulma</TableHead>
                        <TableHead>Son Giriş</TableHead>
                        <TableHead>İşlemler</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {users.map((user) => (
                        <TableRow key={user.id}>
                          <TableCell className="font-medium">{user.username}</TableCell>
                          <TableCell>
                            <span className={`px-2 py-1 rounded-full text-xs ${
                              user.role === 'admin' ? 'bg-red-100 text-red-800' : 'bg-blue-100 text-blue-800'
                            }`}>
                              {user.role}
                            </span>
                          </TableCell>
                          <TableCell>
                            <div className="text-xs space-y-1">
                              {user.permissions.viewUsers && <div>👁️ View Users</div>}
                              {user.permissions.unlockUsers && <div>🔓 Unlock Users</div>}
                              {user.permissions.manageSettings && <div>⚙️ Manage Settings</div>}
                              {user.permissions.manageUsers && <div>👥 Manage Users</div>}
                              {user.permissions.resetPasswords && <div>🔐 Reset Passwords</div>}
                            </div>
                          </TableCell>
                          <TableCell>{formatDate(user.createdAt)}</TableCell>
                          <TableCell>{user.lastLogin ? formatDate(user.lastLogin) : 'Hiç'}</TableCell>
                          <TableCell>
                            <div className="flex space-x-2">
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleEditUser(user)}
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              {user.username !== 'admin' && (
                                <Button
                                  size="sm"
                                  variant="destructive"
                                  onClick={() => handleDeleteUser(user.id)}
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              )}
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )
            )}
          </CardContent>
        </Card>
        </div>
      </main>

      {/* Debug info (only in development) */}
      {process.env.NODE_ENV === 'development' && (
        <div className="fixed bottom-2 right-2 bg-black/80 text-white text-xs p-2 rounded z-50">
          <div>Device: {device.deviceType}</div>
          <div>Width: {device.screenWidth}px</div>
          <div>Mobile: {device.isMobile ? 'Yes' : 'No'}</div>
        </div>
      )}
    </div>
  );
}
