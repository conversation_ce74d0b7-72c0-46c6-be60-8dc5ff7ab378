'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useSession } from 'next-auth/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { useDeviceDetection } from '@/hooks/useDeviceDetection';
import { MobileNav } from '@/components/ui/mobile-nav';
import { DesktopNav } from '@/components/ui/desktop-nav';
import { Users, UserPlus, KeyRound, BarChart3, Settings } from 'lucide-react';

export default function Home() {
  const { data: session } = useSession();
  const device = useDeviceDetection();
  const [expiringUsersCount, setExpiringUsersCount] = useState(0);

  useEffect(() => {
    // Şifre süresi dolacak kullanıcıları getir
    if (session?.user?.permissions?.unlockUsers) {
      fetch('/api/password-expiry')
        .then(response => response.json())
        .then(data => {
          if (data.stats && data.stats.expiringWithin5Days !== undefined) {
            setExpiringUsersCount(data.stats.expiringWithin5Days);
          }
        })
        .catch(error => {
          console.error('Error fetching expiring users:', error);
        });
    }
  }, [session]);

  if (!session) {
    return null;
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Adaptive Navigation */}
      {device.isMobile ? (
        <MobileNav currentPath="/" />
      ) : (
        <DesktopNav currentPath="/" />
      )}

      {/* Main Content */}
      <main className={`
        ${device.isMobile ? 'pb-safe px-4 py-4' : 'container mx-auto px-4 py-6'}
      `}>
        <div className={`${device.isMobile ? 'space-y-4' : 'space-y-6'}`}>
          <div className="text-center">
            <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-foreground mb-2 sm:mb-4">
              Hoş Geldiniz, {session.user.name}
            </h1>
            <p className="text-base sm:text-lg lg:text-xl text-muted-foreground">
              Active Directory Web Yönetim Paneli
            </p>
          </div>

          <div className={`
            grid
            ${device.isMobile ? 'grid-cols-1 gap-4' : device.deviceType === 'tablet' ? 'grid-cols-2 gap-4' : 'grid-cols-3 gap-6'}
          `}>
            {session.user.permissions.unlockUsers && (
              <Card className="hover:shadow-lg transition-shadow">
                <CardHeader className="text-center pb-4">
                  <div className="mx-auto mb-3 flex h-10 w-10 sm:h-12 sm:w-12 items-center justify-center rounded-lg bg-primary/10">
                    <Users className="h-5 w-5 sm:h-6 sm:w-6 text-primary" />
                  </div>
                  <CardTitle className="text-lg sm:text-xl">Locked Users</CardTitle>
                  <CardDescription className="text-sm">
                    Kilitlenen kullanıcıları görüntüle ve unlock yap
                  </CardDescription>
                </CardHeader>
                <CardContent className="text-center pt-0">
                  <Button asChild size="sm" className="w-full sm:w-auto">
                    <Link href="/users">
                      Kullanıcıları Görüntüle
                    </Link>
                  </Button>
                </CardContent>
              </Card>
            )}

            {session.user.permissions.unlockUsers && (
              <Card className="hover:shadow-lg transition-shadow">
                <CardHeader className="text-center">
                  <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-orange-100">
                    <KeyRound className="h-6 w-6 text-orange-600" />
                  </div>
                  <CardTitle>Password Expiry</CardTitle>
                  <CardDescription>
                    Şifre süresi dolan kullanıcıları yönet
                  </CardDescription>
                </CardHeader>
                <CardContent className="text-center">
                  <Button variant="outline" className="border-orange-200 text-orange-700 hover:bg-orange-50" asChild>
                    <Link href="/password-expiry">
                      Şifre Yönetimi
                    </Link>
                  </Button>
                </CardContent>
              </Card>
            )}

            {session.user.permissions.manageSettings && (
              <Card className="hover:shadow-lg transition-shadow">
                <CardHeader className="text-center">
                  <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-secondary/10">
                    <Settings className="h-6 w-6 text-secondary-foreground" />
                  </div>
                  <CardTitle>LDAP Settings</CardTitle>
                  <CardDescription>
                    LDAP bağlantı ayarlarını yapılandır
                  </CardDescription>
                </CardHeader>
                <CardContent className="text-center">
                  <Button variant="secondary" asChild>
                    <Link href="/settings">
                      Ayarları Düzenle
                    </Link>
                  </Button>
                </CardContent>
              </Card>
            )}

            {true && ( {/* Geçici olarak herkese açık */}
              <Card className="hover:shadow-lg transition-shadow">
                <CardHeader className="text-center">
                  <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-red-100">
                    <KeyRound className="h-6 w-6 text-red-600" />
                  </div>
                  <CardTitle>Reset Password</CardTitle>
                  <CardDescription>
                    Kullanıcı şifrelerini sıfırla
                  </CardDescription>
                </CardHeader>
                <CardContent className="text-center">
                  <Button variant="outline" className="border-red-200 text-red-700 hover:bg-red-50" asChild>
                    <Link href="/reset-password">
                      Şifre Sıfırla
                    </Link>
                  </Button>
                </CardContent>
              </Card>
            )}

            {session.user.permissions.manageUsers && (
              <Card className="hover:shadow-lg transition-shadow">
                <CardHeader className="text-center">
                  <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-green-100">
                    <UserPlus className="h-6 w-6 text-green-600" />
                  </div>
                  <CardTitle>User Management</CardTitle>
                  <CardDescription>
                    Sistem kullanıcılarını yönet
                  </CardDescription>
                </CardHeader>
                <CardContent className="text-center">
                  <Button variant="outline" className="border-green-200 text-green-700 hover:bg-green-50" asChild>
                    <Link href="/manage-users">
                      Kullanıcı Yönetimi
                    </Link>
                  </Button>
                </CardContent>
              </Card>
            )}
          </div>

          <div className="bg-muted/50 rounded-lg p-6">
            <h2 className="text-2xl font-semibold text-foreground mb-4">Sistem Durumu</h2>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-3xl font-bold text-primary mb-2">✓</div>
                <div className="text-sm text-muted-foreground">LDAP Bağlantısı</div>
                <div className="text-lg font-medium text-foreground">Aktif</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600 mb-2">🔐</div>
                <div className="text-sm text-muted-foreground">Güvenlik</div>
                <div className="text-lg font-medium text-foreground">Korumalı</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600 mb-2">⚡</div>
                <div className="text-sm text-muted-foreground">Sistem</div>
                <div className="text-lg font-medium text-foreground">Çalışıyor</div>
              </div>
              {session.user.permissions.unlockUsers && (
                <div className="text-center">
                  <div className="text-3xl font-bold text-orange-600 mb-2">⚠️</div>
                  <div className="text-sm text-muted-foreground">Şifre Süresi Dolacak</div>
                  <div className="text-lg font-medium text-foreground">
                    <Link href="/password-expiry" className="text-orange-600 hover:underline">
                      {expiringUsersCount} Kullanıcı
                    </Link>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </main>


    </div>
  );
}
