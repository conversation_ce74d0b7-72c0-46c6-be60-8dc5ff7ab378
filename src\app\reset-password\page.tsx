'use client';

import { useState } from 'react';
import { useSession } from 'next-auth/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useDeviceDetection } from '@/hooks/useDeviceDetection';
import { useNotifications, NotificationContainer } from '@/components/ui/notification';
import { MobileNav } from '@/components/ui/mobile-nav';
import { DesktopNav } from '@/components/ui/desktop-nav';
import { Loader2, Search, KeyRound, User, AlertTriangle, CheckCircle, Eye, EyeOff, Copy } from 'lucide-react';

interface UserInfo {
  username: string;
  displayName: string;
  email?: string;
  department?: string;
  lastLogon?: string;
  passwordLastSet?: string;
  isDisabled: boolean;
}

interface ResetResult {
  success: boolean;
  newPassword?: string;
  resetBy?: string;
  resetAt?: string;
}

export default function ResetPassword() {
  const { data: session } = useSession();
  const device = useDeviceDetection();
  const { notifications, addNotification, removeNotification } = useNotifications();

  const [searchUsername, setSearchUsername] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [searchError, setSearchError] = useState('');
  const [foundUser, setFoundUser] = useState<UserInfo | null>(null);
  const [isResetting, setIsResetting] = useState(false);
  const [resetResult, setResetResult] = useState<ResetResult | null>(null);
  const [showPassword, setShowPassword] = useState(false);

  // Early return if no session
  if (!session) {
    return null; // Will be redirected by middleware
  }

  // Check permissions
  if (!session.user.permissions.resetPasswords) {
    return (
      <div className="min-h-screen bg-background">
        {device.isMobile ? <MobileNav /> : <DesktopNav />}
        <div className="container mx-auto px-4 py-8">
          <Alert className="border-red-200 bg-red-50">
            <AlertTriangle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-800">
              Bu sayfaya erişim yetkiniz bulunmamaktadır. Şifre sıfırlama yetkisi gereklidir.
            </AlertDescription>
          </Alert>
        </div>
      </div>
    );
  }

  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return 'Hiç';
    try {
      return new Date(dateString).toLocaleString('tr-TR');
    } catch {
      return 'Geçersiz tarih';
    }
  };

  const searchUser = async () => {
    if (!searchUsername.trim()) {
      setSearchError('Kullanıcı adı gereklidir');
      return;
    }

    setIsSearching(true);
    setSearchError('');
    setFoundUser(null);
    setResetResult(null);

    try {
      const response = await fetch(`/api/reset-password?username=${encodeURIComponent(searchUsername.trim())}`);
      const data = await response.json();

      if (response.ok) {
        setFoundUser(data.user);
        addNotification({
          title: 'Başarılı',
          description: `Kullanıcı bulundu: ${data.user.displayName}`,
          variant: 'success',
          duration: 3000
        });
      } else {
        setSearchError(data.error || 'Kullanıcı bulunamadı');
        addNotification({
          title: 'Hata',
          description: `${data.error || 'Kullanıcı bulunamadı'}`,
          variant: 'error',
          duration: 5000
        });
      }
    } catch (error) {
      console.error('Search error:', error);
      setSearchError('Arama sırasında bir hata oluştu');
      addNotification({
        title: 'Hata',
        description: 'Arama sırasında bir hata oluştu',
        variant: 'error',
        duration: 5000
      });
    } finally {
      setIsSearching(false);
    }
  };

  const resetPassword = async () => {
    if (!foundUser) return;

    setIsResetting(true);

    try {
      const response = await fetch('/api/reset-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username: foundUser.username
        }),
      });

      const data = await response.json();

      if (response.ok) {
        setResetResult(data);
        addNotification({
          title: 'Başarılı',
          description: `${foundUser.displayName} kullanıcısının şifresi başarıyla sıfırlandı!`,
          variant: 'success',
          duration: 5000
        });
      } else {
        addNotification({
          title: 'Hata',
          description: `Şifre sıfırlama hatası: ${data.error}`,
          variant: 'error',
          duration: 5000
        });
      }
    } catch (error) {
      console.error('Reset error:', error);
      addNotification({
        title: 'Hata',
        description: 'Şifre sıfırlama sırasında bir hata oluştu',
        variant: 'error',
        duration: 5000
      });
    } finally {
      setIsResetting(false);
    }
  };

  const copyPassword = async () => {
    if (resetResult?.newPassword) {
      try {
        await navigator.clipboard.writeText(resetResult.newPassword);
        addNotification({
          title: 'Başarılı',
          description: 'Şifre panoya kopyalandı',
          variant: 'success',
          duration: 2000
        });
      } catch (error) {
        console.error('Copy error:', error);
        addNotification({
          title: 'Hata',
          description: 'Kopyalama başarısız',
          variant: 'error',
          duration: 3000
        });
      }
    }
  };

  const startNewSearch = () => {
    setSearchUsername('');
    setFoundUser(null);
    setResetResult(null);
    setSearchError('');
  };

  return (
    <div className="min-h-screen bg-background">
      {device.isMobile ? <MobileNav /> : <DesktopNav />}
      
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto space-y-6">
          {/* Header */}
          <div className="text-center space-y-2">
            <h1 className="text-3xl font-bold text-foreground">Şifre Sıfırlama</h1>
            <p className="text-muted-foreground">
              Kullanıcı şifrelerini sıfırlayın ve yeni şifre oluşturun
            </p>
          </div>

          {/* Search Section */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Search className="h-5 w-5 text-primary" />
                <span>Kullanıcı Arama</span>
              </CardTitle>
              <CardDescription>
                Şifresini sıfırlamak istediğiniz kullanıcının adını girin
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="username">Kullanıcı Adı</Label>
                <div className="flex space-x-2">
                  <Input
                    id="username"
                    type="text"
                    placeholder="Kullanıcı adını girin..."
                    value={searchUsername}
                    onChange={(e) => setSearchUsername(e.target.value)}
                    onKeyDown={(e) => e.key === 'Enter' && searchUser()}
                    disabled={isSearching}
                    className="flex-1"
                  />
                  <Button
                    onClick={searchUser}
                    disabled={isSearching || !searchUsername.trim()}
                    className="px-6"
                  >
                    {isSearching ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Aranıyor...
                      </>
                    ) : (
                      <>
                        <Search className="mr-2 h-4 w-4" />
                        Ara
                      </>
                    )}
                  </Button>
                </div>
              </div>

              {searchError && (
                <Alert className="border-red-200 bg-red-50">
                  <AlertTriangle className="h-4 w-4 text-red-600" />
                  <AlertDescription className="text-red-800">
                    {searchError}
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>

          {/* User Info Section */}
          {foundUser && (
            <Card className="border-green-200">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <User className="h-5 w-5 text-green-600" />
                  <span>Bulunan Kullanıcı</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {/* Desktop View */}
                  {!device.isMobile && (
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label className="text-sm font-medium text-muted-foreground">Kullanıcı Adı</Label>
                        <p className="text-lg font-semibold">{foundUser.username}</p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-muted-foreground">Görünen Ad</Label>
                        <p className="text-lg">{foundUser.displayName}</p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-muted-foreground">E-posta</Label>
                        <p className="text-lg">{foundUser.email || 'Belirtilmemiş'}</p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-muted-foreground">Departman</Label>
                        <p className="text-lg">{foundUser.department || 'Belirtilmemiş'}</p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-muted-foreground">Son Giriş</Label>
                        <p className="text-lg">{formatDate(foundUser.lastLogon)}</p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-muted-foreground">Şifre Son Değişim</Label>
                        <p className="text-lg">{formatDate(foundUser.passwordLastSet)}</p>
                      </div>
                    </div>
                  )}

                  {/* Mobile View */}
                  {device.isMobile && (
                    <div className="space-y-3">
                      <div>
                        <Label className="text-sm font-medium text-muted-foreground">Kullanıcı Adı</Label>
                        <p className="text-lg font-semibold">{foundUser.username}</p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-muted-foreground">Görünen Ad</Label>
                        <p className="text-lg">{foundUser.displayName}</p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-muted-foreground">E-posta</Label>
                        <p className="text-lg">{foundUser.email || 'Belirtilmemiş'}</p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-muted-foreground">Departman</Label>
                        <p className="text-lg">{foundUser.department || 'Belirtilmemiş'}</p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-muted-foreground">Son Giriş</Label>
                        <p className="text-lg">{formatDate(foundUser.lastLogon)}</p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-muted-foreground">Şifre Son Değişim</Label>
                        <p className="text-lg">{formatDate(foundUser.passwordLastSet)}</p>
                      </div>
                    </div>
                  )}

                  {/* Status */}
                  <div className="pt-4 border-t">
                    <div className="flex items-center space-x-2">
                      <span className="text-sm font-medium text-muted-foreground">Durum:</span>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        foundUser.isDisabled 
                          ? 'bg-red-100 text-red-800' 
                          : 'bg-green-100 text-green-800'
                      }`}>
                        {foundUser.isDisabled ? 'Devre Dışı' : 'Aktif'}
                      </span>
                    </div>
                  </div>

                  {/* Reset Button */}
                  <div className="pt-4">
                    <Button
                      onClick={resetPassword}
                      disabled={isResetting || foundUser.isDisabled}
                      className="w-full bg-red-600 hover:bg-red-700"
                      size="lg"
                    >
                      {isResetting ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Şifre Sıfırlanıyor...
                        </>
                      ) : (
                        <>
                          <KeyRound className="mr-2 h-4 w-4" />
                          Şifreyi Sıfırla
                        </>
                      )}
                    </Button>
                    
                    {foundUser.isDisabled && (
                      <p className="text-sm text-red-600 mt-2 text-center">
                        Devre dışı kullanıcıların şifresi sıfırlanamaz
                      </p>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Reset Result Section */}
          {resetResult && resetResult.success && (
            <Card className="border-green-200 bg-green-50">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2 text-green-800">
                  <CheckCircle className="h-5 w-5" />
                  <span>Şifre Başarıyla Sıfırlandı</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div>
                    <Label className="text-sm font-medium text-green-700">Yeni Şifre</Label>
                    <div className="flex items-center space-x-2 mt-1">
                      <div className="flex-1 relative">
                        <Input
                          type={showPassword ? 'text' : 'password'}
                          value={resetResult.newPassword || ''}
                          readOnly
                          className="bg-white border-green-300 pr-20"
                        />
                        <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex space-x-1">
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => setShowPassword(!showPassword)}
                            className="h-6 w-6 p-0"
                          >
                            {showPassword ? (
                              <EyeOff className="h-4 w-4" />
                            ) : (
                              <Eye className="h-4 w-4" />
                            )}
                          </Button>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={copyPassword}
                            className="h-6 w-6 p-0"
                          >
                            <Copy className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <Label className="text-green-700">Sıfırlayan</Label>
                      <p className="text-green-800">{resetResult.resetBy}</p>
                    </div>
                    <div>
                      <Label className="text-green-700">Sıfırlama Zamanı</Label>
                      <p className="text-green-800">{formatDate(resetResult.resetAt)}</p>
                    </div>
                  </div>

                  <Alert className="border-amber-200 bg-amber-50">
                    <AlertTriangle className="h-4 w-4 text-amber-600" />
                    <AlertDescription className="text-amber-800">
                      <strong>Önemli:</strong> Bu şifreyi güvenli bir şekilde kullanıcıya iletin ve kullanıcının ilk girişte şifresini değiştirmesini sağlayın.
                    </AlertDescription>
                  </Alert>

                  <Button
                    onClick={startNewSearch}
                    variant="outline"
                    className="w-full"
                  >
                    Yeni Arama Yap
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      {/* Notification Container */}
      <NotificationContainer
        notifications={notifications}
        onRemove={removeNotification}
      />
    </div>
  );
}
