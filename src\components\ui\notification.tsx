"use client"

import React, { useEffect, useState } from 'react';
import { CheckCircle, XCircle, X } from 'lucide-react';
import { cn } from '@/lib/utils';

interface NotificationProps {
  id: string;
  title: string;
  description: string;
  variant: 'success' | 'error';
  duration?: number;
  onClose: (id: string) => void;
}

export function Notification({ 
  id, 
  title, 
  description, 
  variant, 
  duration = 8000, 
  onClose 
}: NotificationProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [isLeaving, setIsLeaving] = useState(false);

  useEffect(() => {
    // Show animation
    const showTimer = setTimeout(() => setIsVisible(true), 100);
    
    // Auto close
    const closeTimer = setTimeout(() => {
      handleClose();
    }, duration);

    return () => {
      clearTimeout(showTimer);
      clearTimeout(closeTimer);
    };
  }, [duration]);

  const handleClose = () => {
    setIsLeaving(true);
    setTimeout(() => {
      onClose(id);
    }, 300);
  };

  return (
    <div
      className={cn(
        "fixed bottom-4 right-4 z-50 w-96 max-w-sm p-4 rounded-lg shadow-lg border transition-all duration-300 transform",
        {
          "bg-green-50 border-green-200 text-green-800": variant === 'success',
          "bg-red-50 border-red-200 text-red-800": variant === 'error',
          "translate-x-full opacity-0": !isVisible,
          "translate-x-0 opacity-100": isVisible && !isLeaving,
          "translate-x-full opacity-0": isLeaving,
        }
      )}
    >
      <div className="flex items-start space-x-3">
        <div className="flex-shrink-0">
          {variant === 'success' ? (
            <CheckCircle className="h-5 w-5 text-green-600" />
          ) : (
            <XCircle className="h-5 w-5 text-red-600" />
          )}
        </div>
        <div className="flex-1 min-w-0">
          <p className="text-sm font-semibold">{title}</p>
          <p className="text-sm mt-1 whitespace-pre-line">{description}</p>
        </div>
        <button
          onClick={handleClose}
          className="flex-shrink-0 ml-2 p-1 rounded-md hover:bg-black/5 transition-colors"
        >
          <X className="h-4 w-4" />
        </button>
      </div>
    </div>
  );
}

interface NotificationContainerProps {
  notifications: Array<{
    id: string;
    title: string;
    description: string;
    variant: 'success' | 'error';
    duration?: number;
  }>;
  onRemove: (id: string) => void;
}

export function NotificationContainer({ notifications, onRemove }: NotificationContainerProps) {
  return (
    <div className="fixed bottom-0 right-0 z-50 p-4 space-y-2">
      {notifications.map((notification) => (
        <Notification
          key={notification.id}
          {...notification}
          onClose={onRemove}
        />
      ))}
    </div>
  );
}

// Hook for managing notifications
export function useNotifications() {
  const [notifications, setNotifications] = useState<Array<{
    id: string;
    title: string;
    description: string;
    variant: 'success' | 'error';
    duration?: number;
  }>>([]);

  const addNotification = (notification: {
    title: string;
    description: string;
    variant: 'success' | 'error';
    duration?: number;
  }) => {
    const id = Math.random().toString(36).substr(2, 9);
    setNotifications(prev => [...prev, { ...notification, id }]);
  };

  const removeNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  };

  return {
    notifications,
    addNotification,
    removeNotification,
  };
}
