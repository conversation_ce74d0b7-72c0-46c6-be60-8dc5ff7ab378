import bcrypt from 'bcryptjs';
import fs from 'fs';
import path from 'path';

const USERS_FILE = path.join(process.cwd(), 'users.json');

export interface User {
  id: string;
  username: string;
  password: string;
  role: 'admin' | 'user';
  permissions: {
    viewUsers: boolean;
    unlockUsers: boolean;
    manageSettings: boolean;
    manageUsers: boolean;
    resetPasswords: boolean;
  };
  createdAt: string;
  lastLogin?: string;
}

// Default admin user
const defaultAdmin: User = {
  id: '1',
  username: 'admin',
  password: '$2b$10$t5.K7vyPesCG5IR5F5TkUuSSM/QNlar8Wr243/uQuG0yyrw1mJbBK', // 581326Ob
  role: 'admin',
  permissions: {
    viewUsers: true,
    unlockUsers: true,
    manageSettings: true,
    manageUsers: true,
    resetPasswords: true,
  },
  createdAt: new Date().toISOString(),
};

export function initializeUsers() {
  if (!fs.existsSync(USERS_FILE)) {
    fs.writeFileSync(USERS_FILE, JSON.stringify([defaultAdmin], null, 2));
  }
}

export function getUsers(): User[] {
  try {
    if (!fs.existsSync(USERS_FILE)) {
      initializeUsers();
    }
    const data = fs.readFileSync(USERS_FILE, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error('Error reading users file:', error);
    return [defaultAdmin];
  }
}

export function saveUsers(users: User[]) {
  try {
    fs.writeFileSync(USERS_FILE, JSON.stringify(users, null, 2));
  } catch (error) {
    console.error('Error saving users file:', error);
    throw new Error('Failed to save users');
  }
}

export function getUserByUsername(username: string): User | null {
  const users = getUsers();
  return users.find(user => user.username === username) || null;
}

export function getUserById(id: string): User | null {
  const users = getUsers();
  return users.find(user => user.id === id) || null;
}

export function validatePassword(password: string, hashedPassword: string): boolean {
  return bcrypt.compareSync(password, hashedPassword);
}



export function hashPassword(password: string): string {
  return bcrypt.hashSync(password, 10);
}

export function createUser(userData: Omit<User, 'id' | 'createdAt' | 'password'> & { password: string }): User {
  const users = getUsers();
  
  // Check if username already exists
  if (users.find(user => user.username === userData.username)) {
    throw new Error('Username already exists');
  }

  const newUser: User = {
    ...userData,
    id: Date.now().toString(),
    password: hashPassword(userData.password),
    createdAt: new Date().toISOString(),
  };

  users.push(newUser);
  saveUsers(users);
  
  return newUser;
}

export function updateUser(id: string, updates: Partial<Omit<User, 'id' | 'createdAt'>>): User {
  const users = getUsers();
  const userIndex = users.findIndex(user => user.id === id);
  
  if (userIndex === -1) {
    throw new Error('User not found');
  }

  // If password is being updated, hash it
  if (updates.password) {
    updates.password = hashPassword(updates.password);
  }

  users[userIndex] = { ...users[userIndex], ...updates };
  saveUsers(users);
  
  return users[userIndex];
}

export function deleteUser(id: string): boolean {
  const users = getUsers();
  const userIndex = users.findIndex(user => user.id === id);
  
  if (userIndex === -1) {
    return false;
  }

  // Don't allow deleting the default admin
  if (users[userIndex].username === 'admin') {
    throw new Error('Cannot delete default admin user');
  }

  users.splice(userIndex, 1);
  saveUsers(users);
  
  return true;
}

export function updateLastLogin(userId: string) {
  const users = getUsers();
  const userIndex = users.findIndex(user => user.id === userId);
  
  if (userIndex !== -1) {
    users[userIndex].lastLogin = new Date().toISOString();
    saveUsers(users);
  }
}
