import NextAuth from 'next-auth';

declare module 'next-auth' {
  interface Session {
    user: {
      id: string;
      name: string;
      email: string;
      role: 'admin' | 'user';
      permissions: {
        viewUsers: boolean;
        unlockUsers: boolean;
        manageSettings: boolean;
        manageUsers: boolean;
        resetPasswords: boolean;
      };
    };
  }

  interface User {
    id: string;
    name: string;
    email: string;
    role: 'admin' | 'user';
    permissions: {
      viewUsers: boolean;
      unlockUsers: boolean;
      manageSettings: boolean;
      manageUsers: boolean;
      resetPasswords: boolean;
    };
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    role: 'admin' | 'user';
    permissions: {
      viewUsers: boolean;
      unlockUsers: boolean;
      manageSettings: boolean;
      manageUsers: boolean;
      resetPasswords: boolean;
    };
  }
}
