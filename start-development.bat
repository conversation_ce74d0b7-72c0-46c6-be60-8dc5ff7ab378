@echo off
title AD Web Manager - Development Server
color 0B

echo.
echo ========================================
echo   AD Web Manager - Development Server
echo   Mode: Development (Gelistirme)
echo   Port: 3000
echo ========================================
echo.

REM Set development environment
set NODE_ENV=development
echo [INFO] Development mode aktif ✓

REM Kill any existing process on port 3000
echo [INFO] Port 3000 kontrol ediliyor...
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :3000') do (
    taskkill /f /pid %%a >nul 2>&1
)
echo [INFO] Port 3000 temizlendi ✓
echo.

echo ========================================
echo   Development Server Baslatiliyor...
echo ========================================
echo.
echo [INFO] Erisim adresleri:
echo   - Lokal:    http://localhost:3000
echo   - Network:  http://************:3000
echo.
echo [INFO] Ozellikler:
echo   - Hot Reload: Aktif (kod degisince otomatik yenilenir)
echo   - Debug Info: Aktif
echo   - TypeScript: Kontroller aktif
echo   - ESLint: Uyarilar gosterilir
echo.
echo [INFO] Server'i durdurmak icin Ctrl+C basin
echo.
echo ========================================

REM Start the development server
npm run dev

echo.
echo [INFO] Development server durduruldu.
pause
