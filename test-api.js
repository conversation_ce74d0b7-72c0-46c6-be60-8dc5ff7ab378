// API endpoint test script
const http = require('http');

const testEndpoint = (path, method = 'GET', data = null) => {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json',
      }
    };

    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          body: body
        });
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
};

async function runTests() {
  console.log('🧪 API Endpoint Testleri Başlatılıyor...\n');

  // Test 1: Password Expiry API
  try {
    console.log('1️⃣ Password Expiry API testi...');
    const response = await testEndpoint('/api/password-expiry');
    console.log(`   Status: ${response.statusCode}`);
    
    if (response.statusCode === 401) {
      console.log('   ✅ Authentication gerekli (beklenen davranış)');
    } else if (response.statusCode === 200) {
      const data = JSON.parse(response.body);
      console.log(`   ✅ API çalışıyor - Expired: ${data.stats?.totalExpiredUsers || 0}, Expiring Soon: ${data.stats?.expiringSoon || 0}`);
    } else {
      console.log(`   ⚠️  Beklenmeyen status: ${response.statusCode}`);
    }
  } catch (error) {
    console.log(`   ❌ API hatası: ${error.message}`);
  }

  // Test 2: Test Connection API
  try {
    console.log('\n2️⃣ Test Connection API testi...');
    const testData = {
      server: 'test-server',
      port: '389',
      baseDN: 'DC=test,DC=com',
      username: 'test-user',
      password: 'test-pass',
      useSSL: false
    };
    
    const response = await testEndpoint('/api/test-connection', 'POST', testData);
    console.log(`   Status: ${response.statusCode}`);
    
    if (response.statusCode === 400 || response.statusCode === 500) {
      console.log('   ✅ API endpoint çalışıyor (test verisi ile hata beklenir)');
    } else {
      console.log(`   Status: ${response.statusCode}`);
    }
  } catch (error) {
    console.log(`   ❌ API hatası: ${error.message}`);
  }

  // Test 3: Health Check
  try {
    console.log('\n3️⃣ Ana sayfa erişim testi...');
    const response = await testEndpoint('/');
    console.log(`   Status: ${response.statusCode}`);
    
    if (response.statusCode === 200 || response.statusCode === 302) {
      console.log('   ✅ Ana sayfa erişilebilir');
    } else {
      console.log(`   ⚠️  Beklenmeyen status: ${response.statusCode}`);
    }
  } catch (error) {
    console.log(`   ❌ Erişim hatası: ${error.message}`);
  }

  console.log('\n🏁 Test tamamlandı!');
}

runTests();