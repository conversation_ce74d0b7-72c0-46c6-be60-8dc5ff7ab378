// Build test
const { execSync } = require('child_process');
const fs = require('fs');

console.log('🏗️  Build Testi Başlatılıyor...\n');

try {
  console.log('1️⃣ TypeScript kontrolü...');
  execSync('npx tsc --noEmit', { stdio: 'pipe' });
  console.log('   ✅ TypeScript hataları yok');
} catch (error) {
  console.log('   ⚠️  TypeScript uyarıları var (build devam edecek)');
}

try {
  console.log('\n2️⃣ ESLint kontrolü...');
  execSync('npx eslint . --ext .ts,.tsx --max-warnings 0', { stdio: 'pipe' });
  console.log('   ✅ ESLint hataları yok');
} catch (error) {
  console.log('   ⚠️  ESLint uyarıları var');
}

try {
  console.log('\n3️⃣ Next.js build testi...');
  console.log('   ⏳ Build başlatılıyor... (Bu işlem birkaç dakika sürebilir)');
  
  const buildOutput = execSync('npm run build', { 
    encoding: 'utf8',
    timeout: 300000 // 5 dakika timeout
  });
  
  console.log('   ✅ Build başarılı!');
  
  // .next klasörü kontrolü
  if (fs.existsSync('.next')) {
    console.log('   ✅ .next klasörü oluşturuldu');
    
    // Build boyutu kontrolü
    const stats = fs.statSync('.next');
    console.log(`   📊 Build klasörü oluşturuldu`);
  }
  
} catch (error) {
  console.log('   ❌ Build hatası:');
  console.log(error.message);
}

console.log('\n🏁 Build testi tamamlandı!');