// Dependencies test
const { execSync } = require('child_process');
const fs = require('fs');

console.log('📦 Bağımlılıklar Kontrol Ediliyor...\n');

try {
  // Node.js version
  const nodeVersion = process.version;
  console.log(`Node.js: ${nodeVersion}`);
  
  // npm version
  const npmVersion = execSync('npm --version', { encoding: 'utf8' }).trim();
  console.log(`npm: ${npmVersion}`);
  
  // Package.json kontrolü
  if (fs.existsSync('package.json')) {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    console.log(`\n📋 Proje: ${packageJson.name} v${packageJson.version}`);
    
    // Kritik bağımlılıkları kontrol et
    const criticalDeps = ['next', 'react', 'ldapjs', 'next-auth'];
    console.log('\n🔍 Kritik bağımlılıklar:');
    
    criticalDeps.forEach(dep => {
      if (packageJson.dependencies && packageJson.dependencies[dep]) {
        console.log(`   ✅ ${dep}: ${packageJson.dependencies[dep]}`);
      } else {
        console.log(`   ❌ ${dep}: Bulunamadı`);
      }
    });
    
    // node_modules kontrolü
    if (fs.existsSync('node_modules')) {
      console.log('\n✅ node_modules klasörü mevcut');
    } else {
      console.log('\n❌ node_modules klasörü bulunamadı');
      console.log('💡 "npm install" komutunu çalıştırın');
    }
    
  } else {
    console.log('❌ package.json bulunamadı');
  }
  
} catch (error) {
  console.log('❌ Kontrol hatası:', error.message);
}