// LDAP Settings test
const fs = require('fs');
const path = require('path');

console.log('🔧 LDAP Ayarları Kontrol Ediliyor...\n');

const settingsPath = path.join(process.cwd(), 'ldap-settings.json');

if (fs.existsSync(settingsPath)) {
  try {
    const settings = JSON.parse(fs.readFileSync(settingsPath, 'utf8'));
    console.log('✅ LDAP ayarları dosyası bulundu');
    console.log('📋 Mevcut ayarlar:');
    console.log(`   Server: ${settings.server || 'Tanımlı değil'}`);
    console.log(`   Port: ${settings.port || 'Tanımlı değil'}`);
    console.log(`   Base DN: ${settings.baseDN || 'Tanımlı değil'}`);
    console.log(`   Username: ${settings.username ? '✅ Tanımlı' : '❌ Tanımlı değil'}`);
    console.log(`   Password: ${settings.password ? '✅ Tanımlı' : '❌ Tanımlı değil'}`);
    console.log(`   SSL: ${settings.useSSL ? 'Aktif' : 'Pasif'}`);
    
    // Gerekli alanları kontrol et
    const requiredFields = ['server', 'baseDN', 'username', 'password'];
    const missingFields = requiredFields.filter(field => !settings[field]);
    
    if (missingFields.length === 0) {
      console.log('\n✅ Tüm gerekli alanlar tanımlı');
    } else {
      console.log(`\n⚠️  Eksik alanlar: ${missingFields.join(', ')}`);
    }
  } catch (error) {
    console.log('❌ LDAP ayarları dosyası okunamadı:', error.message);
  }
} else {
  console.log('❌ LDAP ayarları dosyası bulunamadı');
  console.log('💡 İlk çalıştırmada otomatik oluşturulacak');
}