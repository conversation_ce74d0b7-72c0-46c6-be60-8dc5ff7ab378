// Logo dosyası kontrolü
const fs = require('fs');
const path = require('path');

console.log('🔍 Logo dosyası kontrol ediliyor...');

const logoPath = path.join(process.cwd(), 'public', 'bayraktar_holding_logo.jpeg');
const altLogoPath = path.join(process.cwd(), 'public', 'Bayraktar Holding Logo.png');

if (fs.existsSync(logoPath)) {
  console.log('✅ Logo dosyası bulundu: bayraktar_holding_logo.jpeg');
} else if (fs.existsSync(altLogoPath)) {
  console.log('✅ Alternatif logo dosyası bulundu: Bayraktar Holding Logo.png');
  console.log('⚠️  Login sayfasında dosya yolu güncellenmeli');
} else {
  console.log('❌ Logo dosyası bulunamadı!');
  console.log('📁 public/ klasöründeki dosyalar:');
  try {
    const publicFiles = fs.readdirSync(path.join(process.cwd(), 'public'));
    publicFiles.forEach(file => console.log(`   - ${file}`));
  } catch (error) {
    console.log('❌ public/ klasörü okunamadı');
  }
}